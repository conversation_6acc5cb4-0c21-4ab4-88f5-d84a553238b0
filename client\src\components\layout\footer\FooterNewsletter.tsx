import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useLanguage } from '@/hooks/use-language';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Mail, Send, CheckCircle } from 'lucide-react';

// Newsletter subscription schema with email validation
const newsletterSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
});

type NewsletterFormData = z.infer<typeof newsletterSchema>;

export function FooterNewsletter() {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const form = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: NewsletterFormData) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call - replace with actual newsletter subscription logic
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For now, we'll just show a success message
      // In a real implementation, you would send this to your newsletter service
      console.log('Newsletter subscription:', data.email);
      
      setIsSubscribed(true);
      form.reset();
      
      toast({
        title: t('newsletter.subscriptionSuccess'),
        description: t('newsletter.subscriptionSuccessMessage'),
        variant: 'default',
      });
      
      // Reset success state after 3 seconds
      setTimeout(() => setIsSubscribed(false), 3000);
      
    } catch (error) {
      toast({
        title: t('newsletter.subscriptionError'),
        description: t('newsletter.subscriptionErrorMessage'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h4 className="font-playfair text-lg font-semibold mb-6 flex items-center">
        <Mail className="text-gold mr-2 h-5 w-5" />
        {t('newsletter.title')}
      </h4>
      
      <p className="text-gray-300 leading-relaxed mb-6 text-sm">
        {t('newsletter.description')}
      </p>

      {isSubscribed ? (
        <div className="flex items-center space-x-3 text-green-400 bg-green-400/10 p-4 rounded-lg">
          <CheckCircle className="h-5 w-5" />
          <span className="text-sm font-medium">{t('newsletter.thankYou')}</span>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={t('newsletter.emailPlaceholder')}
                      className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:border-gold focus:ring-gold"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-red-400 text-xs" />
                </FormItem>
              )}
            />
            
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gold hover:bg-gold/90 text-charcoal font-medium transition-all duration-300 disabled:opacity-50"
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-charcoal border-t-transparent rounded-full animate-spin" />
                  <span>{t('newsletter.subscribing')}</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Send className="h-4 w-4" />
                  <span>{t('newsletter.subscribe')}</span>
                </div>
              )}
            </Button>
          </form>
        </Form>
      )}
      
      <p className="text-gray-400 text-xs mt-4 leading-relaxed">
        {t('newsletter.privacyNote')}
      </p>
    </div>
  );
}
